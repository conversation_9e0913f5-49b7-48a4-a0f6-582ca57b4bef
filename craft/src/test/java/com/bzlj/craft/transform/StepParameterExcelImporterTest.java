package com.bzlj.craft.transform;

import com.bzlj.craft.common.BaseTestNGTest;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.*;
import com.bzlj.craft.transform.data.DataPrepareService;
import com.bzlj.craft.transform.service.ExcelImportPolicy;
import com.bzlj.craft.transform.service.impl.StepParameterExcelImporter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.testng.Assert.*;

public class StepParameterExcelImporterTest extends BaseTestNGTest {

    @Mock
    private CraftProcessRepository craftProcessRepository;

    @Mock
    private ProcessStepRepository processStepRepository;

    @Mock
    private StepParameterRepository stepParameterRepository;

    @Mock
    private DataPrepareService dataPrepareService;

    @InjectMocks
    private StepParameterExcelImporter stepParameterExcelImporter;

    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testUpdateExistingParameter() throws IOException {
        // 测试更新已存在的参数
        ByteArrayInputStream inputStream = createTestExcel();

        CraftProcess mockProcess = createMockCraftProcess();
        ProcessStep mockStep = createMockProcessStep();
        SysDictItem mockParamType = createMockParamType();

        // 创建已存在的参数
        StepParameter existingParam = new StepParameter();
        existingParam.setParamId("existing-param-id");
        existingParam.setStep(mockStep);
        existingParam.setParamCode("PARAM001");
        existingParam.setParamName("旧温度名称");
        existingParam.setParamType(mockParamType);
        existingParam.setParamOrder(2);
        existingParam.setVersion(1);

        when(craftProcessRepository.findFirstByProcessCode("PROCESS001")).thenReturn(mockProcess);
        when(processStepRepository.findByProcessIdOrderByStepOrderAsc(anyString())).thenReturn(List.of(mockStep));
        when(dataPrepareService.getParamTypeDictItem("NUMBER")).thenReturn(mockParamType);
        // 模拟工步下已有一个参数PARAM001
        when(stepParameterRepository.findByStepIdOrderByCreatedTime(anyString())).thenReturn(List.of(existingParam));

        List<StepParameter> result = stepParameterExcelImporter.parseExcel(inputStream);

        // 验证结果
        assertEquals(result.size(), 2);
        
        // 第一个参数是更新的现有参数
        StepParameter updatedParam = result.get(0);
        assertEquals(updatedParam.getParamId(), "existing-param-id"); // 保持原有ID
        assertEquals(updatedParam.getParamCode(), "PARAM001");
        assertEquals(updatedParam.getParamName(), "温度"); // 更新为新名称
        assertEquals(updatedParam.getParamOrder(), Integer.valueOf(2)); // 保持原有顺序
        
        // 第二个参数是新创建的参数
        StepParameter newParam = result.get(1);
        assertNull(newParam.getParamId()); // 新参数没有ID
        assertEquals(newParam.getParamCode(), "PARAM002");
        assertEquals(newParam.getParamName(), "压力");
        assertEquals(newParam.getParamOrder(), Integer.valueOf(2)); // 新参数的顺序（基于已有1个参数）
    }

    @Test
    public void testCreateNewParameters() throws IOException {
        // 测试创建新参数
        ByteArrayInputStream inputStream = createTestExcel();

        CraftProcess mockProcess = createMockCraftProcess();
        ProcessStep mockStep = createMockProcessStep();
        SysDictItem mockParamType = createMockParamType();

        when(craftProcessRepository.findFirstByProcessCode("PROCESS001")).thenReturn(mockProcess);
        when(processStepRepository.findByProcessIdOrderByStepOrderAsc(anyString())).thenReturn(List.of(mockStep));
        when(dataPrepareService.getParamTypeDictItem("NUMBER")).thenReturn(mockParamType);
        // 模拟工步下已有3个参数，但都不是我们要导入的参数
        when(stepParameterRepository.findByStepIdOrderByCreatedTime(anyString())).thenReturn(List.of(
            createExistingParam("OTHER001", "其他参数1"),
            createExistingParam("OTHER002", "其他参数2"),
            createExistingParam("OTHER003", "其他参数3")
        ));

        List<StepParameter> result = stepParameterExcelImporter.parseExcel(inputStream);

        // 验证结果
        assertEquals(result.size(), 2);
        
        StepParameter param1 = result.get(0);
        assertEquals(param1.getParamCode(), "PARAM001");
        assertEquals(param1.getParamName(), "温度");
        assertEquals(param1.getParamOrder(), Integer.valueOf(4)); // 数据库已有3个，新的从4开始

        StepParameter param2 = result.get(1);
        assertEquals(param2.getParamCode(), "PARAM002");
        assertEquals(param2.getParamName(), "压力");
        assertEquals(param2.getParamOrder(), Integer.valueOf(5)); // 第二个参数是5
    }

    @Test(expectedExceptions = RuntimeException.class, expectedExceptionsMessageRegExp = ".*Excel中存在重复的参数.*")
    public void testDuplicateParamCodeInExcel() throws IOException {
        // 测试Excel中重复参数代码的校验
        ByteArrayInputStream inputStream = createDuplicateExcel();

        CraftProcess mockProcess = createMockCraftProcess();
        ProcessStep mockStep = createMockProcessStep();
        SysDictItem mockParamType = createMockParamType();

        when(craftProcessRepository.findFirstByProcessCode("PROCESS001")).thenReturn(mockProcess);
        when(processStepRepository.findByProcessIdOrderByStepOrderAsc(anyString())).thenReturn(List.of(mockStep));
        when(dataPrepareService.getParamTypeDictItem("NUMBER")).thenReturn(mockParamType);

        // 应该抛出异常
        stepParameterExcelImporter.parseExcel(inputStream);
    }

    private ByteArrayInputStream createTestExcel() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("工艺参数");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("工序");
        headerRow.createCell(1).setCellValue("参数Code");
        headerRow.createCell(2).setCellValue("参数名称");
        headerRow.createCell(3).setCellValue("参数类型");

        // 创建测试数据
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue("PROCESS001");
        row1.createCell(1).setCellValue("PARAM001");
        row1.createCell(2).setCellValue("温度");
        row1.createCell(3).setCellValue("NUMBER");

        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue("PROCESS001");
        row2.createCell(1).setCellValue("PARAM002");
        row2.createCell(2).setCellValue("压力");
        row2.createCell(3).setCellValue("NUMBER");

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    private ByteArrayInputStream createDuplicateExcel() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("工艺参数");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("工序");
        headerRow.createCell(1).setCellValue("参数Code");
        headerRow.createCell(2).setCellValue("参数名称");
        headerRow.createCell(3).setCellValue("参数类型");

        // 创建重复的测试数据
        Row row1 = sheet.createRow(1);
        row1.createCell(0).setCellValue("PROCESS001");
        row1.createCell(1).setCellValue("PARAM001");
        row1.createCell(2).setCellValue("温度");
        row1.createCell(3).setCellValue("NUMBER");

        Row row2 = sheet.createRow(2);
        row2.createCell(0).setCellValue("PROCESS001");
        row2.createCell(1).setCellValue("PARAM001"); // 重复的参数代码
        row2.createCell(2).setCellValue("温度2");
        row2.createCell(3).setCellValue("NUMBER");

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    private CraftProcess createMockCraftProcess() {
        CraftProcess process = new CraftProcess();
        process.setId("process-id-001");
        process.setProcessCode("PROCESS001");
        process.setProcessName("测试工序");
        process.setProcessOrder(1);
        process.setInputOutputSpec(Map.of());
        return process;
    }

    private ProcessStep createMockProcessStep() {
        ProcessStep step = new ProcessStep();
        step.setId("step-id-001");
        step.setStepCode("STEP001");
        step.setStepName("测试工步");
        step.setStepOrder(1);
        step.setParamConfig(Map.of());
        step.setQualityStandard(Map.of());
        return step;
    }

    private SysDictItem createMockParamType() {
        SysDictItem dictItem = new SysDictItem();
        dictItem.setItemCode("NUMBER");
        dictItem.setItemName("数值型");
        dictItem.setSortOrder(1);
        dictItem.setIsActive(true);
        return dictItem;
    }

    private StepParameter createExistingParam(String paramCode, String paramName) {
        StepParameter param = new StepParameter();
        param.setParamId("existing-" + paramCode);
        param.setParamCode(paramCode);
        param.setParamName(paramName);
        param.setParamType(createMockParamType());
        param.setParamOrder(1);
        param.setVersion(1);
        return param;
    }
}
